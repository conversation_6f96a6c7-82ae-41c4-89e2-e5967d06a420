const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

app.use(cors());
app.use(express.json());

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

const PORT = process.env.PORT || 4000;

// In-memory storage for messages and users
const messages = {
  general: [],
  random: [],
  announcements: []
};
const onlineUsers = [];

io.on('connection', (socket) => {
  console.log('A user connected:', socket.id);

  // Handle user joining
  socket.on('join', (userData) => {
    socket.userData = userData;
    onlineUsers.push({ id: socket.id, ...userData });
    console.log(`${userData.username} joined the chat`);
    
    // Broadcast to all clients that a user joined
    io.emit('user_joined', {
      username: userData.username,
      onlineUsers: onlineUsers
    });
    
    // Send current online users to the new user
    socket.emit('online_users', onlineUsers);
  });

  // Handle channel join
  socket.on('join_channel', (data) => {
    socket.join(data.channel);
    console.log(`${data.user} joined channel: ${data.channel}`);
  });

  // Handle getting channel messages
  socket.on('get_channel_messages', (channel) => {
    socket.emit('channel_messages', messages[channel] || []);
  });
  
  // Handle new messages
  socket.on('message', (message) => {
    console.log('Message received:', message);
    
    // Store message in memory
    if (!messages[message.channel]) {
      messages[message.channel] = [];
    }
    messages[message.channel].push(message);
    
    // Broadcast message to all clients
    io.emit('message', message);
  });

  // Handle user disconnect
  socket.on('disconnect', () => {
    if (socket.userData) {
      const userIndex = onlineUsers.findIndex(user => user.id === socket.id);
      if (userIndex !== -1) {
        const disconnectedUser = onlineUsers.splice(userIndex, 1)[0];
        console.log(`${disconnectedUser.username} left the chat`);
        
        // Broadcast to all clients that a user left
        io.emit('user_left', {
          username: disconnectedUser.username,
          onlineUsers: onlineUsers
        });
      }
    }
    console.log('A user disconnected:', socket.id);
  });
});

// Root route to serve the chat interface
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

server.listen(PORT, () => {
  console.log(`🚀 Employee Chat System running on http://localhost:${PORT}`);
  console.log(`📱 Open your browser and navigate to: http://localhost:${PORT}`);
});
