const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const { v4: uuidv4 } = require('uuid');
// Temporarily disabled for testing
// const auth = require('./auth');
// const database = require('./database');

// Load environment variables
dotenv.config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false // Disable for development
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Auth rate limiting (stricter)
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5 // limit each IP to 5 auth requests per windowMs
});

app.use(cors());
app.use(express.json());

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

const PORT = process.env.PORT || 4000;

// Simplified authentication routes for testing
app.post('/api/auth/register', authLimiter, async (req, res) => {
  try {
    const { username, email, password } = req.body;

    // Basic validation
    if (!username || !email || !password) {
      return res.status(400).json({ error: 'Username, email, and password are required' });
    }

    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters long' });
    }

    // Simplified registration - just return success
    const user = { id: Date.now().toString(), username, email, role: 'user' };
    res.status(201).json({ message: 'User registered successfully', user });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

app.post('/api/auth/login', authLimiter, async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Simplified login - accept any credentials for testing
    const user = { id: Date.now().toString(), username, email: `${username}@example.com`, role: 'user' };
    const token = 'simple-test-token-' + Date.now();

    res.json({
      message: 'Login successful',
      user: user,
      token: token
    });
  } catch (error) {
    res.status(401).json({ error: error.message });
  }
});

app.get('/api/auth/me', (req, res) => {
  // Simplified - just return a test user
  res.json({ user: { id: '1', username: 'TestUser', email: '<EMAIL>', role: 'user' } });
});

app.get('/api/users', (req, res) => {
  // Simplified - return test users
  const users = [
    { id: '1', username: 'TestUser', email: '<EMAIL>', role: 'user' },
    { id: '2', username: 'Admin', email: '<EMAIL>', role: 'admin' }
  ];
  res.json({ users });
});

// Database is now used for persistent storage

// Temporarily disable socket authentication for testing
// io.use(auth.authenticateSocket);

io.on('connection', (socket) => {
  console.log('A user connected:', socket.id);

  // Handle user joining (simplified for testing)
  socket.on('join', (userData) => {
    socket.userData = userData || { username: 'TestUser', email: '<EMAIL>' };

    console.log(`${socket.userData.username} joined the chat`);

    // Simple online users array for testing
    const onlineUsers = [socket.userData];

    // Broadcast to all clients that a user joined
    io.emit('user_joined', {
      username: socket.userData.username,
      onlineUsers: onlineUsers
    });

    // Send current online users to the new user
    socket.emit('online_users', onlineUsers);
  });

  // Handle channel join
  socket.on('join_channel', (data) => {
    socket.join(data.channel);
    console.log(`${data.user} joined channel: ${data.channel}`);
  });

  // Handle getting channel messages (simplified)
  socket.on('get_channel_messages', (channel) => {
    // Return empty messages for now
    socket.emit('channel_messages', []);
  });
  
  // Handle new messages (simplified)
  socket.on('message', (message) => {
    console.log('Message received:', message);

    // Create the message object to broadcast
    const broadcastMessage = {
      id: uuidv4(),
      user: socket.userData?.username || 'Anonymous',
      text: message.text,
      channel: message.channel,
      timestamp: new Date().toISOString()
    };

    // Broadcast message to all clients
    io.emit('message', broadcastMessage);
  });

  // Handle typing indicators
  socket.on('typing_start', (data) => {
    socket.broadcast.emit('user_typing', data);
  });

  socket.on('typing_stop', (data) => {
    socket.broadcast.emit('user_stop_typing', data);
  });

  // Handle user disconnect (simplified)
  socket.on('disconnect', () => {
    if (socket.userData) {
      console.log(`${socket.userData.username} left the chat`);

      // Broadcast to all clients that a user left
      io.emit('user_left', {
        username: socket.userData.username,
        onlineUsers: []
      });
    }
    console.log('A user disconnected:', socket.id);
  });
});

// Root route - serve chat interface (protected)
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Login route
app.get('/login', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

server.listen(PORT, () => {
  console.log(`🚀 Employee Chat System running on http://localhost:${PORT}`);
  console.log(`📱 Open your browser and navigate to: http://localhost:${PORT}`);
});
