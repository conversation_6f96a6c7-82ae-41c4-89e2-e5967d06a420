body, html {
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
  background-color: #f4f7fa;
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hidden {
  display: none;
}

.login-screen {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  text-align: center;
}

.login-header h1 {
  font-size: 24px;
  margin-bottom: 10px;
  color: #333;
}

.login-header p {
  font-size: 14px;
  color: #777;
}

.login-form .form-group {
  position: relative;
  margin-bottom: 20px;
}

.login-form input {
  width: calc(100% - 40px);
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 30px;
  border: 1px solid #ddd;
}

.login-form i {
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
  color: #888;
}

.login-btn {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  color: white;
  background: #007bff;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: background 0.3s;
}

.login-btn:hover {
  background: #0056b3;
}

.chat-interface {
  display: flex;
  width: 100%;
  height: 100vh;
}

.sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  background: #34495e;
}

.sidebar-header h2 {
  font-size: 18px;
  margin: 0;
}

.sidebar-header .user-info {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.user-avatar {
  width: 35px;
  height: 35px;
  background: #2980b9;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.user-info .username {
  font-weight: bold;
  margin-right: 5px;
}

.channels-list, .users-list {
  padding: 10px;
  flex-grow: 1;
  overflow-y: auto;
}

.channels-list .channel-item, .users-list .user-item {
  padding: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background 0.3s;
}

.channel-item:hover, .user-item:hover {
  background: #34495e;
}

.channel-item.active {
  background: #2980b9;
}

.main-chat {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background: #ecf0f1;
}

.chat-header {
  background: #3498db;
  color: white;
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
  align-items: center;
}

.chat-header h2, .chat-header p {
  margin: 0;
}

.messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
}

.message-input-container {
  padding: 15px 20px;
  background: #bdc3c7;
}

.message-form {
  display: flex;
  align-items: center;
}

.input-wrapper {
  position: relative;
  flex-grow: 1;
}

.input-wrapper input {
  width: 100%;
  padding: 10px 15px 10px 35px;
  border-radius: 20px;
  border: 1px solid #ddd;
  font-size: 14px;
}

.input-wrapper .emoji-btn, .input-wrapper .attachment-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #7f8c8d;
}

.emoji-btn {
  right: 40px;
}

.attachment-btn {
  right: 10px;
}

.send-btn {
  margin-left: 15px;
  padding: 10px 20px;
  background: #2ecc71;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: background 0.3s;
}

.send-btn:hover {
  background: #27ae60;
}

/* Message Styles */
.message {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
  background: white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  animation: fadeIn 0.3s ease-in;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.message-user {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.message-user.own-message {
  color: #2980b9;
}

.message-time {
  font-size: 12px;
  color: #7f8c8d;
}

.message-content {
  line-height: 1.4;
  color: #34495e;
  word-wrap: break-word;
}

.system-message {
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
  padding: 10px;
  margin-bottom: 10px;
  background: #ecf0f1;
  border-radius: 4px;
  font-size: 13px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-interface {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: 200px;
    order: 2;
  }
  
  .main-chat {
    order: 1;
    height: calc(100vh - 200px);
  }
  
  .channels-section, .online-users {
    display: none;
  }
}

/* Action buttons */
.action-btn {
  background: none;
  border: none;
  color: white;
  padding: 8px 10px;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.3s;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Channel and user items */
.channel-item i, .user-item i {
  margin-right: 8px;
  width: 16px;
  text-align: center;
}

.channels-section h3, .online-users h3 {
  margin: 15px 0 10px 0;
  font-size: 14px;
  color: #95a5a6;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background: #27ae60;
  color: white;
}

/* Input focus styles */
.login-form input:focus, .input-wrapper input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Button hover effects */
.emoji-btn:hover, .attachment-btn:hover {
  color: #3498db;
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #bdc3c7;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #95a5a6;
}
