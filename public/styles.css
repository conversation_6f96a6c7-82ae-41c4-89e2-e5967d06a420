:root {
  /* Light theme colors */
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --secondary-color: #8b5cf6;
  --accent-color: #06b6d4;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-chat: #ffffff;
  --bg-sidebar: #1e293b;
  --bg-header: #0f172a;

  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;

  --border-color: #e2e8f0;
  --border-hover: #cbd5e1;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --backdrop-blur: blur(16px);
}

[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-chat: #1e293b;
  --bg-sidebar: #0f172a;
  --bg-header: #020617;

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;

  --border-color: #334155;
  --border-hover: #475569;

  --glass-bg: rgba(15, 23, 42, 0.25);
  --glass-border: rgba(148, 163, 184, 0.18);
}

* {
  box-sizing: border-box;
}

body, html {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  color: var(--text-primary);
  transition: background 0.3s ease, color 0.3s ease;
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.hidden {
  display: none !important;
}

/* Theme Toggle Button */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-primary);
}

.theme-toggle:hover {
  transform: scale(1.1);
  background: var(--primary-color);
  color: white;
}

.login-screen {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  padding: 40px;
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-xl);
  border-radius: 24px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.login-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.login-header {
  margin-bottom: 32px;
}

.login-header h1 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-header p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.login-form .form-group {
  position: relative;
  margin-bottom: 24px;
}

.login-form input {
  width: 100%;
  padding: 16px 20px 16px 50px;
  font-size: 16px;
  border-radius: 16px;
  border: 2px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.3s ease;
  font-family: inherit;
}

.login-form input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
  transform: translateY(-2px);
}

.login-form input::placeholder {
  color: var(--text-tertiary);
}

.login-form i {
  position: absolute;
  top: 50%;
  left: 18px;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  font-size: 18px;
  transition: color 0.3s ease;
}

.login-form .form-group:focus-within i {
  color: var(--primary-color);
}

.login-btn {
  width: 100%;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.login-btn:hover::before {
  left: 100%;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.login-btn:active {
  transform: translateY(0);
}

.chat-interface {
  display: flex;
  width: 100%;
  height: 100vh;
  background: var(--bg-secondary);
  position: relative;
}

.sidebar {
  width: 280px;
  background: var(--bg-sidebar);
  color: var(--text-inverse);
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border-color);
  position: relative;
  z-index: 10;
}

.sidebar::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
  opacity: 0.5;
}

.sidebar-header {
  padding: 24px 20px;
  background: var(--bg-header);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.sidebar-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

.sidebar-header h2 {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 16px 0;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-header .user-info {
  display: flex;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.sidebar-header .user-info:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  font-size: 16px;
  position: relative;
  overflow: hidden;
}

.user-avatar::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.user-details {
  flex: 1;
}

.user-info .username {
  font-weight: 600;
  font-size: 14px;
  display: block;
  margin-bottom: 2px;
}

.channels-section, .online-users {
  padding: 0 16px;
  margin-bottom: 24px;
}

.channels-section h3, .online-users h3 {
  margin: 20px 0 12px 0;
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 1px;
  padding-left: 8px;
}

.channels-list, .users-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.channels-list .channel-item, .users-list .user-item {
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.channel-item::before, .user-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: var(--primary-color);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.channel-item:hover, .user-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.channel-item:hover::before, .user-item:hover::before {
  transform: scaleY(1);
}

.channel-item.active {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  font-weight: 600;
}

.channel-item.active::before {
  transform: scaleY(1);
  background: white;
}

.channel-item i, .user-item i {
  margin-right: 12px;
  width: 16px;
  text-align: center;
  font-size: 14px;
}

.channel-item.active i {
  color: white;
}

.main-chat {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-chat);
  position: relative;
}

.chat-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  display: flex;
  justify-content: space-between;
  padding: 20px 24px;
  align-items: center;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 5;
}

.chat-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 24px;
  right: 24px;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.3;
}

.channel-info h2 {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: var(--text-primary);
}

.channel-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 24px;
  background: var(--bg-secondary);
  position: relative;
}

.messages-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(180deg, var(--bg-primary), transparent);
  pointer-events: none;
  z-index: 1;
}

.message-input-container {
  padding: 20px 24px;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  position: relative;
}

.message-input-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 24px;
  right: 24px;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.3;
}

.message-form {
  display: flex;
  align-items: center;
  gap: 12px;
}

.input-wrapper {
  position: relative;
  flex-grow: 1;
}

.input-wrapper input {
  width: 100%;
  padding: 16px 60px 16px 20px;
  border-radius: 24px;
  border: 2px solid var(--border-color);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 16px;
  font-family: inherit;
  transition: all 0.3s ease;
  resize: none;
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
  background: var(--bg-primary);
}

.input-wrapper input::placeholder {
  color: var(--text-tertiary);
}

.input-wrapper .emoji-btn, .input-wrapper .attachment-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--text-tertiary);
  background: none;
  border: none;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 16px;
}

.input-wrapper .emoji-btn:hover, .input-wrapper .attachment-btn:hover {
  color: var(--primary-color);
  background: var(--bg-tertiary);
}

.emoji-btn {
  right: 44px;
}

.attachment-btn {
  right: 8px;
}

.send-btn {
  padding: 16px 20px;
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  position: relative;
  overflow: hidden;
}

.send-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.send-btn:hover::before {
  left: 100%;
}

.send-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.send-btn:active {
  transform: translateY(0);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Message Styles */
.message {
  margin-bottom: 20px;
  padding: 16px 20px;
  border-radius: 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  animation: messageSlideIn 0.4s ease-out;
  position: relative;
  transition: all 0.3s ease;
}

.message:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.message::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
  border-radius: 16px 0 0 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.message:hover::before {
  opacity: 1;
}

.message.own-message {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  margin-left: 40px;
  border: none;
}

.message.own-message::before {
  background: white;
  opacity: 0.3;
}

.message.own-message:hover::before {
  opacity: 0.5;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-user {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.message.own-message .message-user {
  color: rgba(255, 255, 255, 0.9);
}

.message-user::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success-color);
  display: inline-block;
}

.message.own-message .message-user::before {
  background: rgba(255, 255, 255, 0.7);
}

.message-time {
  font-size: 12px;
  color: var(--text-tertiary);
  opacity: 0.7;
}

.message.own-message .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.message-content {
  line-height: 1.6;
  color: var(--text-primary);
  word-wrap: break-word;
  font-size: 15px;
}

.message.own-message .message-content {
  color: white;
}

.system-message {
  text-align: center;
  color: var(--text-tertiary);
  font-style: italic;
  padding: 12px 16px;
  margin: 16px 0;
  background: var(--bg-tertiary);
  border-radius: 12px;
  font-size: 13px;
  border: 1px solid var(--border-color);
  position: relative;
}

.system-message::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 16px;
  right: 16px;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
  z-index: 0;
}

.system-message span {
  background: var(--bg-tertiary);
  padding: 0 12px;
  position: relative;
  z-index: 1;
}

/* Animations */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes slideInFromLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Action buttons */
.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
  padding: 10px 12px;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.5s;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-btn:active {
  transform: translateY(0);
}

/* Status indicator */
.status {
  font-size: 11px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
  background: var(--success-color);
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.status::before {
  content: '';
  position: absolute;
  left: -2px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar,
.channels-list::-webkit-scrollbar,
.users-list::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track,
.channels-list::-webkit-scrollbar-track,
.users-list::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb,
.channels-list::-webkit-scrollbar-thumb,
.users-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.messages-container::-webkit-scrollbar-thumb:hover,
.channels-list::-webkit-scrollbar-thumb:hover,
.users-list::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 260px;
  }

  .login-screen {
    max-width: 380px;
    padding: 32px;
  }
}

@media (max-width: 768px) {
  .chat-interface {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    order: 2;
    border-right: none;
    border-top: 1px solid var(--border-color);
  }

  .main-chat {
    order: 1;
    height: 60vh;
  }

  .channels-section, .online-users {
    padding: 0 12px;
  }

  .channels-section h3, .online-users h3 {
    margin: 12px 0 8px 0;
  }

  .sidebar-header {
    padding: 16px;
  }

  .login-screen {
    max-width: 340px;
    padding: 24px;
    margin: 20px;
  }

  .theme-toggle {
    top: 16px;
    right: 16px;
    width: 44px;
    height: 44px;
  }
}

@media (max-width: 480px) {
  .message {
    margin-left: 0;
    margin-right: 0;
  }

  .message.own-message {
    margin-left: 20px;
  }

  .chat-header {
    padding: 16px;
  }

  .messages-container {
    padding: 16px;
  }

  .message-input-container {
    padding: 16px;
  }

  .login-screen {
    margin: 16px;
    padding: 20px;
  }

  .login-header h1 {
    font-size: 28px;
  }
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  color: var(--text-tertiary);
  font-style: italic;
  font-size: 14px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--text-tertiary);
  animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDots {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

/* Focus states */
.login-form input:focus,
.input-wrapper input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

/* Emoji Picker */
.emoji-picker {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px 16px 0 0;
  box-shadow: var(--shadow-xl);
  max-height: 300px;
  z-index: 1000;
  backdrop-filter: var(--backdrop-blur);
}

.emoji-categories {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  gap: 4px;
}

.emoji-category {
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.3s ease;
}

.emoji-category:hover {
  background: var(--bg-tertiary);
}

.emoji-category.active {
  background: var(--primary-color);
  color: white;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-item {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 20px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-item:hover {
  background: var(--bg-tertiary);
  transform: scale(1.2);
}

/* File Upload & Drop Zone */
.drop-zone {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(99, 102, 241, 0.1);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border: 3px dashed var(--primary-color);
  border-radius: 16px;
  margin: 20px;
}

.drop-zone-content {
  text-align: center;
  color: var(--primary-color);
}

.drop-zone-content i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.drop-zone-content p {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.drop-zone.drag-over {
  background: rgba(99, 102, 241, 0.2);
  border-color: var(--primary-hover);
}

.file-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  margin: 8px 0;
  font-size: 14px;
}

.file-preview .file-icon {
  color: var(--primary-color);
}

.file-preview .file-name {
  flex: 1;
  color: var(--text-primary);
}

.file-preview .file-size {
  color: var(--text-tertiary);
  font-size: 12px;
}

.file-preview .remove-file {
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.file-preview .remove-file:hover {
  background: rgba(239, 68, 68, 0.1);
}

/* Search Bar */
.search-bar {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 16px 24px;
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-wrapper input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.3s ease;
}

.search-input-wrapper input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.search-close-btn {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 8px;
  margin-left: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-close-btn:hover {
  color: var(--error-color);
  background: var(--bg-tertiary);
}

.search-results {
  max-height: 200px;
  overflow-y: auto;
  margin-top: 12px;
}

.search-result-item {
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
  border-left: 3px solid transparent;
}

.search-result-item:hover {
  background: var(--bg-tertiary);
  border-left-color: var(--primary-color);
}

.search-result-user {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 13px;
  margin-bottom: 4px;
}

.search-result-content {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.search-result-time {
  color: var(--text-tertiary);
  font-size: 12px;
  margin-top: 4px;
}

.search-highlight {
  background: rgba(255, 235, 59, 0.3);
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
}

/* Utility classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}

.slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}
