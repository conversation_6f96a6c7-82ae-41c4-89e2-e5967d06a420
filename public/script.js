// Check authentication first
function checkAuth() {
    const token = localStorage.getItem('authToken');
    const userData = localStorage.getItem('userData');

    if (!token || !userData) {
        window.location.href = '/login.html';
        return false;
    }

    return { token, userData: JSON.parse(userData) };
}

const authData = checkAuth();
if (!authData) return;

// Socket.IO connection with authentication
const socket = io({
    auth: {
        token: authData.token
    }
});

// DOM elements
const loginScreen = document.getElementById('loginScreen');
const chatInterface = document.getElementById('chatInterface');
const loginForm = document.getElementById('loginForm');
const messageForm = document.getElementById('messageForm');
const messageInput = document.getElementById('messageInput');
const messagesContainer = document.getElementById('messagesContainer');
const currentUsernameEl = document.getElementById('currentUsername');
const channelsList = document.getElementById('channelsList');
const currentChannelEl = document.getElementById('currentChannel');
const channelDescriptionEl = document.getElementById('channelDescription');
const onlineUsersListEl = document.getElementById('onlineUsersList');
const logoutBtn = document.getElementById('logoutBtn');
const themeToggle = document.getElementById('themeToggle');
const themeIcon = document.getElementById('themeIcon');
const emojiBtn = document.getElementById('emojiBtn');
const emojiPicker = document.getElementById('emojiPicker');
const emojiGrid = document.getElementById('emojiGrid');
const attachmentBtn = document.getElementById('attachmentBtn');
const fileInput = document.getElementById('fileInput');
const dropZone = document.getElementById('dropZone');
const searchBtn = document.getElementById('searchBtn');
const searchBar = document.getElementById('searchBar');
const searchInput = document.getElementById('searchInput');
const searchCloseBtn = document.getElementById('searchCloseBtn');
const searchResults = document.getElementById('searchResults');

// User data
let currentUser = authData.userData;
let currentChannel = 'general';
let onlineUsers = [];
let isTyping = false;
let typingTimeout = null;
let allMessages = []; // Store all messages for search

// Channel descriptions
const channelDescriptions = {
    'general': 'Team discussions and general chat',
    'random': 'Random conversations and fun stuff',
    'announcements': 'Important company announcements'
};

// Theme management
const initTheme = () => {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateThemeIcon(savedTheme);
};

const toggleTheme = () => {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);
};

const updateThemeIcon = (theme) => {
    themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
};

// Initialize theme on load
initTheme();

// Auto-join chat since user is authenticated
joinChat();

// Join chat
function joinChat() {
    socket.emit('join');
    loginScreen.classList.add('hidden');
    chatInterface.classList.remove('hidden');
    currentUsernameEl.textContent = currentUser.username;

    // Load initial messages for general channel
    loadChannelMessages(currentChannel);
}

// Message form handler
messageForm.addEventListener('submit', (e) => {
    e.preventDefault();
    const text = messageInput.value.trim();
    
    if (text) {
        const message = {
            id: Date.now(),
            user: currentUser.username,
            text: text,
            channel: currentChannel,
            timestamp: new Date().toISOString()
        };
        
        socket.emit('message', message);
        messageInput.value = '';
    }
});

// Channel switching
channelsList.addEventListener('click', (e) => {
    const channelItem = e.target.closest('.channel-item');
    if (channelItem) {
        const newChannel = channelItem.dataset.channel;
        switchChannel(newChannel);
    }
});

// Switch channel
function switchChannel(channelName) {
    // Update active channel in UI
    document.querySelectorAll('.channel-item').forEach(item => {
        item.classList.remove('active');
    });
    
    document.querySelector(`[data-channel="${channelName}"]`).classList.add('active');
    
    // Update current channel
    currentChannel = channelName;
    currentChannelEl.textContent = `# ${channelName}`;
    channelDescriptionEl.textContent = channelDescriptions[channelName] || 'Channel description';
    
    // Clear messages and load new channel messages
    messagesContainer.innerHTML = '';
    loadChannelMessages(channelName);
    
    // Emit channel change
    socket.emit('join_channel', { channel: channelName, user: currentUser.username });
}

// Load channel messages
function loadChannelMessages(channelName) {
    socket.emit('get_channel_messages', channelName);
}

// Logout handler
logoutBtn.addEventListener('click', () => {
    // Clear authentication data
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');

    // Disconnect socket
    socket.disconnect();

    // Redirect to login
    window.location.href = '/login.html';
});

// Socket event handlers
socket.on('message', (message) => {
    if (message.channel === currentChannel) {
        displayMessage(message);
    }
});

socket.on('channel_messages', (messages) => {
    messagesContainer.innerHTML = '';
    messages.forEach(message => {
        // Convert database message format to client format
        const clientMessage = {
            id: message.id,
            user: message.username,
            text: message.content,
            channel: message.channel,
            timestamp: message.created_at
        };
        displayMessage(clientMessage);
    });
});

socket.on('user_joined', (data) => {
    displaySystemMessage(`${data.username} joined the chat`);
    updateOnlineUsers(data.onlineUsers);
});

socket.on('user_left', (data) => {
    displaySystemMessage(`${data.username} left the chat`);
    updateOnlineUsers(data.onlineUsers);
});

socket.on('online_users', (users) => {
    updateOnlineUsers(users);
});

socket.on('user_typing', (data) => {
    if (data.channel === currentChannel && data.user !== currentUser.username) {
        showTypingIndicator(data.user);
    }
});

socket.on('user_stop_typing', (data) => {
    if (data.channel === currentChannel) {
        hideTypingIndicator(data.user);
    }
});

// Handle authentication errors
socket.on('connect_error', (error) => {
    console.error('Socket connection error:', error);
    if (error.message === 'Authentication error') {
        // Clear invalid token and redirect to login
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
        window.location.href = '/login.html';
    }
});

// Display message in chat
function displayMessage(message) {
    const messageEl = document.createElement('div');
    const isOwnMessage = message.user === currentUser.username;

    messageEl.className = `message ${isOwnMessage ? 'own-message' : ''}`;
    messageEl.setAttribute('data-message-id', message.id);

    messageEl.innerHTML = `
        <div class="message-header">
            <span class="message-user">${message.user}</span>
            <span class="message-time">${formatTime(message.timestamp)}</span>
        </div>
        <div class="message-content">${escapeHtml(message.text)}</div>
    `;

    messagesContainer.appendChild(messageEl);

    // Store message for search
    if (!allMessages.find(m => m.id === message.id)) {
        allMessages.push(message);
    }

    scrollToBottom();

    // Add entrance animation
    setTimeout(() => {
        messageEl.style.opacity = '1';
        messageEl.style.transform = 'translateY(0)';
    }, 10);
}

// Display system message
function displaySystemMessage(text) {
    const messageEl = document.createElement('div');
    messageEl.className = 'system-message';
    messageEl.innerHTML = `<span>${text}</span>`;
    messagesContainer.appendChild(messageEl);
    scrollToBottom();
}

// Update online users list
function updateOnlineUsers(users) {
    onlineUsers = users;
    onlineUsersListEl.innerHTML = '';

    users.forEach((user, index) => {
        const userEl = document.createElement('div');
        userEl.className = 'user-item';
        userEl.style.animationDelay = `${index * 0.1}s`;
        userEl.innerHTML = `
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-details">
                <span class="username">${user.username}</span>
                <span class="status">Online</span>
            </div>
        `;
        onlineUsersListEl.appendChild(userEl);
    });
}

// Utility functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Enhanced message input handling
messageInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        messageForm.dispatchEvent(new Event('submit'));
    }
});

// Add typing indicator functionality
messageInput.addEventListener('input', () => {
    if (!isTyping) {
        isTyping = true;
        socket.emit('typing_start', { user: currentUser.username, channel: currentChannel });
    }

    clearTimeout(typingTimeout);
    typingTimeout = setTimeout(() => {
        isTyping = false;
        socket.emit('typing_stop', { user: currentUser.username, channel: currentChannel });
    }, 1000);
});

// Auto-focus message input when chat loads
setTimeout(() => {
    if (messageInput) {
        messageInput.focus();
    }
}, 100);

// Add smooth scrolling
function scrollToBottom() {
    messagesContainer.scrollTo({
        top: messagesContainer.scrollHeight,
        behavior: 'smooth'
    });
}

// Enhanced utility functions
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
        return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + ' ' +
               date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
}

// Add message reactions (placeholder for future enhancement)
function addMessageReaction(messageId, reaction) {
    // This will be implemented in the advanced features phase
    console.log(`Adding reaction ${reaction} to message ${messageId}`);
}

// Add notification support
function showNotification(title, body) {
    if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
            body: body,
            icon: '/favicon.ico'
        });
    }
}

// Request notification permission
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}

// Typing indicator functionality
let typingUsers = new Set();

function showTypingIndicator(username) {
    typingUsers.add(username);
    updateTypingIndicator();
}

function hideTypingIndicator(username) {
    typingUsers.delete(username);
    updateTypingIndicator();
}

function updateTypingIndicator() {
    let existingIndicator = document.querySelector('.typing-indicator');

    if (typingUsers.size === 0) {
        if (existingIndicator) {
            existingIndicator.remove();
        }
        return;
    }

    const typingArray = Array.from(typingUsers);
    let typingText = '';

    if (typingArray.length === 1) {
        typingText = `${typingArray[0]} is typing`;
    } else if (typingArray.length === 2) {
        typingText = `${typingArray[0]} and ${typingArray[1]} are typing`;
    } else {
        typingText = `${typingArray.length} people are typing`;
    }

    if (!existingIndicator) {
        existingIndicator = document.createElement('div');
        existingIndicator.className = 'typing-indicator';
        messagesContainer.appendChild(existingIndicator);
    }

    existingIndicator.innerHTML = `
        <span>${typingText}</span>
        <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
        </div>
    `;

    scrollToBottom();
}

// Emoji picker functionality
const emojiCategories = {
    smileys: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳'],
    people: ['👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜', '👏', '🙌', '👐', '🤲', '🤝', '🙏'],
    nature: ['🌱', '🌿', '🍀', '🍃', '🌾', '🌵', '🌲', '🌳', '🌴', '🌸', '🌺', '🌻', '🌹', '🥀', '🌷', '🌼', '🌙', '⭐', '🌟', '✨', '⚡', '🔥', '💧', '🌈', '☀️', '🌤️', '⛅', '🌦️', '🌧️', '⛈️'],
    food: ['🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🌽', '🥕', '🧄', '🧅', '🥔', '🍠', '🥐', '🍞', '🥖'],
    activities: ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️'],
    travel: ['🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛺', '🚨', '🚔', '🚍', '🚘', '🚖', '🚡', '🚠', '🚟', '🚃', '🚋', '🚞'],
    objects: ['💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸', '💵', '💴', '💶', '💷', '💰', '💳', '💎', '⚖️', '🧰', '🔧', '🔨', '⚒️', '🛠️', '⛏️', '🔩', '⚙️', '🧱', '⛓️', '🧲', '🔫', '💣', '🧨', '🪓'],
    symbols: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐']
};

let currentEmojiCategory = 'smileys';

// Event listeners
themeToggle.addEventListener('click', toggleTheme);
emojiBtn.addEventListener('click', toggleEmojiPicker);

// Initialize emoji picker
function initEmojiPicker() {
    // Add category click handlers
    document.querySelectorAll('.emoji-category').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const category = e.target.dataset.category;
            switchEmojiCategory(category);
        });
    });

    // Load initial category
    switchEmojiCategory(currentEmojiCategory);

    // Close emoji picker when clicking outside
    document.addEventListener('click', (e) => {
        if (!emojiPicker.contains(e.target) && !emojiBtn.contains(e.target)) {
            emojiPicker.classList.add('hidden');
        }
    });
}

function toggleEmojiPicker() {
    emojiPicker.classList.toggle('hidden');
}

function switchEmojiCategory(category) {
    currentEmojiCategory = category;

    // Update active category button
    document.querySelectorAll('.emoji-category').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-category="${category}"]`).classList.add('active');

    // Load emojis for category
    loadEmojis(category);
}

function loadEmojis(category) {
    const emojis = emojiCategories[category] || [];
    emojiGrid.innerHTML = '';

    emojis.forEach(emoji => {
        const emojiBtn = document.createElement('button');
        emojiBtn.className = 'emoji-item';
        emojiBtn.textContent = emoji;
        emojiBtn.addEventListener('click', () => insertEmoji(emoji));
        emojiGrid.appendChild(emojiBtn);
    });
}

function insertEmoji(emoji) {
    const cursorPos = messageInput.selectionStart;
    const textBefore = messageInput.value.substring(0, cursorPos);
    const textAfter = messageInput.value.substring(cursorPos);

    messageInput.value = textBefore + emoji + textAfter;
    messageInput.focus();
    messageInput.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

    emojiPicker.classList.add('hidden');
}

// Initialize emoji picker when DOM is loaded
initEmojiPicker();

// File upload functionality
let selectedFiles = [];

// Event listeners for file upload
attachmentBtn.addEventListener('click', () => fileInput.click());
fileInput.addEventListener('change', handleFileSelect);

// Drag and drop functionality
messagesContainer.addEventListener('dragover', handleDragOver);
messagesContainer.addEventListener('dragleave', handleDragLeave);
messagesContainer.addEventListener('drop', handleFileDrop);

function handleFileSelect(e) {
    const files = Array.from(e.target.files);
    processFiles(files);
}

function handleDragOver(e) {
    e.preventDefault();
    dropZone.classList.remove('hidden');
    dropZone.classList.add('drag-over');
}

function handleDragLeave(e) {
    e.preventDefault();
    if (!messagesContainer.contains(e.relatedTarget)) {
        dropZone.classList.add('hidden');
        dropZone.classList.remove('drag-over');
    }
}

function handleFileDrop(e) {
    e.preventDefault();
    dropZone.classList.add('hidden');
    dropZone.classList.remove('drag-over');

    const files = Array.from(e.dataTransfer.files);
    processFiles(files);
}

function processFiles(files) {
    // Filter files by size (max 10MB per file)
    const maxSize = 10 * 1024 * 1024; // 10MB
    const validFiles = files.filter(file => {
        if (file.size > maxSize) {
            showNotification('File too large', `${file.name} is larger than 10MB`);
            return false;
        }
        return true;
    });

    selectedFiles = [...selectedFiles, ...validFiles];
    displayFilePreview();
}

function displayFilePreview() {
    // This would show file previews in the input area
    // For now, we'll just show a simple indicator
    if (selectedFiles.length > 0) {
        messageInput.placeholder = `${selectedFiles.length} file(s) selected. Type a message...`;
    } else {
        messageInput.placeholder = 'Type a message...';
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileIcon(fileName) {
    const extension = fileName.split('.').pop().toLowerCase();
    const iconMap = {
        'pdf': 'fas fa-file-pdf',
        'doc': 'fas fa-file-word',
        'docx': 'fas fa-file-word',
        'txt': 'fas fa-file-alt',
        'jpg': 'fas fa-file-image',
        'jpeg': 'fas fa-file-image',
        'png': 'fas fa-file-image',
        'gif': 'fas fa-file-image',
        'mp4': 'fas fa-file-video',
        'mp3': 'fas fa-file-audio',
        'wav': 'fas fa-file-audio'
    };
    return iconMap[extension] || 'fas fa-file';
}

// Search functionality
searchBtn.addEventListener('click', toggleSearch);
searchCloseBtn.addEventListener('click', closeSearch);
searchInput.addEventListener('input', handleSearch);

function toggleSearch() {
    searchBar.classList.toggle('hidden');
    if (!searchBar.classList.contains('hidden')) {
        searchInput.focus();
    } else {
        searchInput.value = '';
        searchResults.innerHTML = '';
    }
}

function closeSearch() {
    searchBar.classList.add('hidden');
    searchInput.value = '';
    searchResults.innerHTML = '';
}

function handleSearch() {
    const query = searchInput.value.trim().toLowerCase();

    if (query.length < 2) {
        searchResults.innerHTML = '';
        return;
    }

    const filteredMessages = allMessages.filter(message =>
        message.channel === currentChannel &&
        (message.text.toLowerCase().includes(query) ||
         message.user.toLowerCase().includes(query))
    );

    displaySearchResults(filteredMessages, query);
}

function displaySearchResults(messages, query) {
    if (messages.length === 0) {
        searchResults.innerHTML = '<div class="search-result-item">No messages found</div>';
        return;
    }

    searchResults.innerHTML = '';

    messages.slice(0, 10).forEach(message => { // Limit to 10 results
        const resultItem = document.createElement('div');
        resultItem.className = 'search-result-item';

        const highlightedText = highlightSearchTerm(message.text, query);

        resultItem.innerHTML = `
            <div class="search-result-user">${message.user}</div>
            <div class="search-result-content">${highlightedText}</div>
            <div class="search-result-time">${formatTime(message.timestamp)}</div>
        `;

        resultItem.addEventListener('click', () => {
            scrollToMessage(message.id);
            closeSearch();
        });

        searchResults.appendChild(resultItem);
    });
}

function highlightSearchTerm(text, query) {
    const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
    return escapeHtml(text).replace(regex, '<span class="search-highlight">$1</span>');
}

function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function scrollToMessage(messageId) {
    const messageEl = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageEl) {
        messageEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
        messageEl.style.backgroundColor = 'rgba(99, 102, 241, 0.1)';
        setTimeout(() => {
            messageEl.style.backgroundColor = '';
        }, 2000);
    }
}
