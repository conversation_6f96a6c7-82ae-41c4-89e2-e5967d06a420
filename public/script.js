// Socket.IO connection
const socket = io();

// DOM elements
const loginScreen = document.getElementById('loginScreen');
const chatInterface = document.getElementById('chatInterface');
const loginForm = document.getElementById('loginForm');
const messageForm = document.getElementById('messageForm');
const messageInput = document.getElementById('messageInput');
const messagesContainer = document.getElementById('messagesContainer');
const currentUsernameEl = document.getElementById('currentUsername');
const channelsList = document.getElementById('channelsList');
const currentChannelEl = document.getElementById('currentChannel');
const channelDescriptionEl = document.getElementById('channelDescription');
const onlineUsersListEl = document.getElementById('onlineUsersList');
const logoutBtn = document.getElementById('logoutBtn');

// User data
let currentUser = null;
let currentChannel = 'general';
let onlineUsers = [];

// Channel descriptions
const channelDescriptions = {
    'general': 'Team discussions and general chat',
    'random': 'Random conversations and fun stuff',
    'announcements': 'Important company announcements'
};

// Login form handler
loginForm.addEventListener('submit', (e) => {
    e.preventDefault();
    const username = document.getElementById('username').value.trim();
    const email = document.getElementById('email').value.trim();
    
    if (username && email) {
        currentUser = { username, email };
        joinChat();
    }
});

// Join chat
function joinChat() {
    socket.emit('join', currentUser);
    loginScreen.classList.add('hidden');
    chatInterface.classList.remove('hidden');
    currentUsernameEl.textContent = currentUser.username;
    
    // Load initial messages for general channel
    loadChannelMessages(currentChannel);
}

// Message form handler
messageForm.addEventListener('submit', (e) => {
    e.preventDefault();
    const text = messageInput.value.trim();
    
    if (text) {
        const message = {
            id: Date.now(),
            user: currentUser.username,
            text: text,
            channel: currentChannel,
            timestamp: new Date().toISOString()
        };
        
        socket.emit('message', message);
        messageInput.value = '';
    }
});

// Channel switching
channelsList.addEventListener('click', (e) => {
    const channelItem = e.target.closest('.channel-item');
    if (channelItem) {
        const newChannel = channelItem.dataset.channel;
        switchChannel(newChannel);
    }
});

// Switch channel
function switchChannel(channelName) {
    // Update active channel in UI
    document.querySelectorAll('.channel-item').forEach(item => {
        item.classList.remove('active');
    });
    
    document.querySelector(`[data-channel="${channelName}"]`).classList.add('active');
    
    // Update current channel
    currentChannel = channelName;
    currentChannelEl.textContent = `# ${channelName}`;
    channelDescriptionEl.textContent = channelDescriptions[channelName] || 'Channel description';
    
    // Clear messages and load new channel messages
    messagesContainer.innerHTML = '';
    loadChannelMessages(channelName);
    
    // Emit channel change
    socket.emit('join_channel', { channel: channelName, user: currentUser.username });
}

// Load channel messages
function loadChannelMessages(channelName) {
    socket.emit('get_channel_messages', channelName);
}

// Logout handler
logoutBtn.addEventListener('click', () => {
    socket.disconnect();
    loginScreen.classList.remove('hidden');
    chatInterface.classList.add('hidden');
    currentUser = null;
    messagesContainer.innerHTML = '';
    document.getElementById('username').value = '';
    document.getElementById('email').value = '';
});

// Socket event handlers
socket.on('message', (message) => {
    if (message.channel === currentChannel) {
        displayMessage(message);
    }
});

socket.on('channel_messages', (messages) => {
    messagesContainer.innerHTML = '';
    messages.forEach(message => {
        displayMessage(message);
    });
});

socket.on('user_joined', (data) => {
    displaySystemMessage(`${data.username} joined the chat`);
    updateOnlineUsers(data.onlineUsers);
});

socket.on('user_left', (data) => {
    displaySystemMessage(`${data.username} left the chat`);
    updateOnlineUsers(data.onlineUsers);
});

socket.on('online_users', (users) => {
    updateOnlineUsers(users);
});

// Display message in chat
function displayMessage(message) {
    const messageEl = document.createElement('div');
    messageEl.className = 'message';
    
    const isOwnMessage = message.user === currentUser.username;
    
    messageEl.innerHTML = `
        <div class="message-header">
            <span class="message-user ${isOwnMessage ? 'own-message' : ''}">${message.user}</span>
            <span class="message-time">${formatTime(message.timestamp)}</span>
        </div>
        <div class="message-content">${escapeHtml(message.text)}</div>
    `;
    
    messagesContainer.appendChild(messageEl);
    scrollToBottom();
}

// Display system message
function displaySystemMessage(text) {
    const messageEl = document.createElement('div');
    messageEl.className = 'system-message';
    messageEl.textContent = text;
    messagesContainer.appendChild(messageEl);
    scrollToBottom();
}

// Update online users list
function updateOnlineUsers(users) {
    onlineUsers = users;
    onlineUsersListEl.innerHTML = '';
    
    users.forEach(user => {
        const userEl = document.createElement('div');
        userEl.className = 'user-item';
        userEl.innerHTML = `
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <span class="username">${user.username}</span>
        `;
        onlineUsersListEl.appendChild(userEl);
    });
}

// Utility functions
function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function scrollToBottom() {
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Enter key to send message
messageInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        messageForm.dispatchEvent(new Event('submit'));
    }
});

// Auto-focus message input
messageInput.focus();
