<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Chat System</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="themeToggle" title="Toggle theme">
        <i class="fas fa-moon" id="themeIcon"></i>
    </button>

    <div id="app">
        <!-- Login Screen -->
        <div id="loginScreen" class="login-screen">
            <div class="login-container">
                <div class="login-header">
                    <h1><i class="fas fa-comments"></i> Employee Chat</h1>
                    <p>Connect with your team instantly</p>
                </div>
                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <input type="text" id="username" placeholder="Enter your username" required>
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="form-group">
                        <input type="email" id="email" placeholder="Enter your email" required>
                        <i class="fas fa-envelope"></i>
                    </div>
                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i> Join Chat
                    </button>
                </form>
            </div>
        </div>

        <!-- Main Chat Interface -->
        <div id="chatInterface" class="chat-interface hidden">
            <div class="chat-container">
                <!-- Sidebar -->
                <div class="sidebar">
                    <div class="sidebar-header">
                        <h2><i class="fas fa-building"></i> Your Company</h2>
                        <div class="user-info">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-details">
                                <span id="currentUsername" class="username"></span>
                                <span class="status online">Online</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="channels-section">
                        <h3><i class="fas fa-hashtag"></i> Channels</h3>
                        <div id="channelsList" class="channels-list">
                            <div class="channel-item active" data-channel="general">
                                <i class="fas fa-hashtag"></i>
                                <span>general</span>
                            </div>
                            <div class="channel-item" data-channel="random">
                                <i class="fas fa-hashtag"></i>
                                <span>random</span>
                            </div>
                            <div class="channel-item" data-channel="announcements">
                                <i class="fas fa-bullhorn"></i>
                                <span>announcements</span>
                            </div>
                        </div>
                    </div>

                    <div class="online-users">
                        <h3><i class="fas fa-users"></i> Online Users</h3>
                        <div id="onlineUsersList" class="users-list">
                            <!-- Dynamic content -->
                        </div>
                    </div>
                </div>

                <!-- Main Chat Area -->
                <div class="main-chat">
                    <div class="chat-header">
                        <div class="channel-info">
                            <h2 id="currentChannel"># general</h2>
                            <p id="channelDescription">Team discussions and general chat</p>
                        </div>
                        <div class="chat-actions">
                            <button class="action-btn" id="searchBtn" title="Search messages">
                                <i class="fas fa-search"></i>
                            </button>
                            <button class="action-btn" title="Settings">
                                <i class="fas fa-cog"></i>
                            </button>
                            <button id="logoutBtn" class="action-btn" title="Logout">
                                <i class="fas fa-sign-out-alt"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Search Bar -->
                    <div id="searchBar" class="search-bar hidden">
                        <div class="search-input-wrapper">
                            <input type="text" id="searchInput" placeholder="Search messages..." autocomplete="off">
                            <button id="searchCloseBtn" class="search-close-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div id="searchResults" class="search-results"></div>
                    </div>

                    <div id="messagesContainer" class="messages-container">
                        <!-- Messages will be populated here -->
                        <div id="dropZone" class="drop-zone hidden">
                            <div class="drop-zone-content">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>Drop files here to upload</p>
                            </div>
                        </div>
                    </div>

                    <div class="message-input-container">
                        <!-- Emoji Picker -->
                        <div id="emojiPicker" class="emoji-picker hidden">
                            <div class="emoji-categories">
                                <button class="emoji-category active" data-category="smileys">😀</button>
                                <button class="emoji-category" data-category="people">👋</button>
                                <button class="emoji-category" data-category="nature">🌱</button>
                                <button class="emoji-category" data-category="food">🍎</button>
                                <button class="emoji-category" data-category="activities">⚽</button>
                                <button class="emoji-category" data-category="travel">🚗</button>
                                <button class="emoji-category" data-category="objects">💡</button>
                                <button class="emoji-category" data-category="symbols">❤️</button>
                            </div>
                            <div class="emoji-grid" id="emojiGrid">
                                <!-- Emojis will be populated here -->
                            </div>
                        </div>

                        <form id="messageForm" class="message-form">
                            <div class="input-wrapper">
                                <input type="text" id="messageInput" placeholder="Type a message..." autocomplete="off">
                                <button type="button" class="emoji-btn" id="emojiBtn" title="Add emoji">
                                    <i class="fas fa-smile"></i>
                                </button>
                                <button type="button" class="attachment-btn" id="attachmentBtn" title="Attach file">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                <input type="file" id="fileInput" multiple accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt" style="display: none;">
                            </div>
                            <button type="submit" class="send-btn" title="Send message">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
