const Database = require('better-sqlite3');
const path = require('path');
const bcrypt = require('bcrypt');

// Initialize database
const dbPath = path.join(__dirname, 'chat.db');
const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Create tables
const initDatabase = () => {
  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      role TEXT DEFAULT 'user',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Channels table
  db.exec(`
    CREATE TABLE IF NOT EXISTS channels (
      id TEXT PRIMARY KEY,
      name TEXT UNIQUE NOT NULL,
      description TEXT,
      created_by TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOR<PERSON><PERSON><PERSON> KEY (created_by) REFERENCES users(id)
    )
  `);

  // Messages table
  db.exec(`
    CREATE TABLE IF NOT EXISTS messages (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      username TEXT NOT NULL,
      channel TEXT NOT NULL,
      content TEXT NOT NULL,
      message_type TEXT DEFAULT 'text',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id),
      FOREIGN KEY (channel) REFERENCES channels(name)
    )
  `);

  // User sessions table (for tracking online users)
  db.exec(`
    CREATE TABLE IF NOT EXISTS user_sessions (
      id TEXT PRIMARY KEY,
      user_id TEXT NOT NULL,
      socket_id TEXT UNIQUE NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `);

  // Create default channels
  const insertChannel = db.prepare(`
    INSERT OR IGNORE INTO channels (id, name, description)
    VALUES (?, ?, ?)
  `);

  insertChannel.run('general', 'general', 'Team discussions and general chat');
  insertChannel.run('random', 'random', 'Random conversations and fun stuff');
  insertChannel.run('announcements', 'announcements', 'Important company announcements');

  // Create default admin user
  createDefaultAdmin();
};

const createDefaultAdmin = async () => {
  const existingAdmin = db.prepare('SELECT id FROM users WHERE username = ?').get('admin');
  
  if (!existingAdmin) {
    const hashedPassword = await bcrypt.hash('admin123', 10);
    const insertUser = db.prepare(`
      INSERT INTO users (id, username, email, password, role)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    insertUser.run('admin-001', 'admin', '<EMAIL>', hashedPassword, 'admin');
    console.log('Default admin user created: admin / admin123');
  }
};

// Initialize database first
initDatabase();

// User operations
const userOperations = {
  create: db.prepare(`
    INSERT INTO users (id, username, email, password, role)
    VALUES (?, ?, ?, ?, ?)
  `),

  findByUsername: db.prepare('SELECT * FROM users WHERE username = ?'),
  findByEmail: db.prepare('SELECT * FROM users WHERE email = ?'),
  findById: db.prepare('SELECT * FROM users WHERE id = ?'),
  getAll: db.prepare('SELECT id, username, email, role, created_at FROM users'),

  update: db.prepare(`
    UPDATE users
    SET username = ?, email = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),

  delete: db.prepare('DELETE FROM users WHERE id = ?')
};

// Message operations
const messageOperations = {
  create: db.prepare(`
    INSERT INTO messages (id, user_id, username, channel, content, message_type)
    VALUES (?, ?, ?, ?, ?, ?)
  `),
  
  getByChannel: db.prepare(`
    SELECT * FROM messages 
    WHERE channel = ? 
    ORDER BY created_at ASC
  `),
  
  getRecent: db.prepare(`
    SELECT * FROM messages 
    WHERE channel = ? 
    ORDER BY created_at DESC 
    LIMIT ?
  `),
  
  search: db.prepare(`
    SELECT * FROM messages 
    WHERE channel = ? AND (content LIKE ? OR username LIKE ?)
    ORDER BY created_at DESC
    LIMIT 50
  `),
  
  delete: db.prepare('DELETE FROM messages WHERE id = ?'),
  
  deleteByChannel: db.prepare('DELETE FROM messages WHERE channel = ?')
};

// Session operations
const sessionOperations = {
  create: db.prepare(`
    INSERT OR REPLACE INTO user_sessions (id, user_id, socket_id, last_seen)
    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
  `),
  
  update: db.prepare(`
    UPDATE user_sessions 
    SET last_seen = CURRENT_TIMESTAMP 
    WHERE socket_id = ?
  `),
  
  delete: db.prepare('DELETE FROM user_sessions WHERE socket_id = ?'),
  deleteByUserId: db.prepare('DELETE FROM user_sessions WHERE user_id = ?'),
  
  getOnlineUsers: db.prepare(`
    SELECT u.id, u.username, u.email, u.role, s.last_seen
    FROM users u
    JOIN user_sessions s ON u.id = s.user_id
    WHERE s.last_seen > datetime('now', '-5 minutes')
    ORDER BY s.last_seen DESC
  `),
  
  findBySocketId: db.prepare('SELECT * FROM user_sessions WHERE socket_id = ?')
};

// Channel operations
const channelOperations = {
  getAll: db.prepare('SELECT * FROM channels ORDER BY name'),
  findByName: db.prepare('SELECT * FROM channels WHERE name = ?'),
  create: db.prepare(`
    INSERT INTO channels (id, name, description, created_by)
    VALUES (?, ?, ?, ?)
  `),
  delete: db.prepare('DELETE FROM channels WHERE name = ?')
};

// Database is initialized above before creating prepared statements

// Export database operations
module.exports = {
  db,
  users: userOperations,
  messages: messageOperations,
  sessions: sessionOperations,
  channels: channelOperations,
  
  // Utility functions
  close: () => db.close(),
  
  // Transaction helper
  transaction: (fn) => {
    const transaction = db.transaction(fn);
    return transaction;
  }
};
