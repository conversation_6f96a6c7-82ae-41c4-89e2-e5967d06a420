{"name": "employee-chat-system", "version": "1.0.0", "description": "Real-time messaging system for employees", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"bcrypt": "^5.1.0", "better-sqlite3": "^12.2.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-local": "^1.0.0", "socket.io": "^4.7.2", "sqlite3": "^5.1.7", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["chat", "messaging", "real-time", "websocket"], "author": "Your Company", "license": "MIT"}